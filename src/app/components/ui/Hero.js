'use client';
import { getTranslation } from '@/lib/i18n';
import { useState, useEffect } from 'react';

export default function Hero({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const [timestamp, setTimestamp] = useState(0);

  return (
    <div className="text-center pt-16 pb-2">
      <h1 className="text-5xl font-bold text-primary mb-2">
        {t('Unix Timestamp Converter')}
      </h1>
      <div className="section text-left mt-20 px-40">
        <div className="border rounded px-6 py-4">
          <h2 className='text-2xl font-bold mb-4'>{t('Current Unix Timestamp')}</h2>
        </div>
      </div>
    </div>
  );
}
