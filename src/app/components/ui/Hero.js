'use client';
import { getTranslation } from '@/lib/i18n';
import { useState, useEffect, useRef } from 'react';
import { Button } from "@heroui/react";

export default function Hero({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const [timestamp, setTimestamp] = useState(Date.now());
  const [unit, setUnit] = useState('second');
  const [isRunning, setIsRunning] = useState(true);
  const intervalRef = useRef(null);

  // 获取当前时间戳，根据单位返回相应值
  const getCurrentTimestamp = () => {
    const now = Date.now();
    return unit === 'second' ? Math.floor(now / 1000) : now;
  };

  // 定时器效果
  useEffect(() => {
    if (isRunning) {
      intervalRef.current = setInterval(() => {
        setTimestamp(getCurrentTimestamp());
      }, 100); // 每100ms更新一次
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning, unit]);

  // 初始化时间戳
  useEffect(() => {
    setTimestamp(getCurrentTimestamp());
  }, [unit]);

  // 切换单位
  const toggleUnit = () => {
    const newUnit = unit === 'second' ? 'millisecond' : 'second';
    setUnit(newUnit);
  };

  // 开始/停止定时器
  const toggleTimer = () => {
    setIsRunning(!isRunning);
  };

  // 复制到剪贴板
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(timestamp.toString());
      // 可以添加一个提示，但这里先简单实现
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  return (
    <div className="text-center pt-16 pb-2">
      <h1 className="text-5xl font-bold text-primary mb-2">
        {t('Unix Timestamp Converter')}
      </h1>
      <div className="section text-left mt-20 md:px-40">
        <div className="border rounded px-6 py-4">
          <h2 className='text-2xl font-bold mb-4'>{t('Current Unix Timestamp')}</h2>
          <div className="flex items-end gap-2 mb-4">
            <p className='text-xl'>{timestamp}</p>
            <p>{t(unit)}</p>
          </div>
          <div className='flex items-center gap-2'>
            <Button onPress={toggleUnit}>{t('s ⇌ ms')}</Button>
            <Button onPress={copyToClipboard}>{t('Copy')}</Button>
            {isRunning ? (
              <Button onPress={toggleTimer}>{t('Stop')}</Button>
            ) : (
              <Button onPress={toggleTimer}>{t('Start')}</Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
